package usecase

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/bucket"
	"gitlab.com/backend/api-hrm/core/util/cast"
	maputil "gitlab.com/backend/api-hrm/core/util/map"
	"gitlab.com/backend/api-hrm/domain"
)

type employeeUseCase struct {
	employeeRepository domain.EmployeeRepository
}

// NewEmployeeUseCase func
func NewEmployeeUseCase(e domain.EmployeeRepository) domain.EmployeeUseCase {
	return &employeeUseCase{employeeRepository: e}
}

func (e *employeeUseCase) Fetch(adminFkid int) ([]domain.Employee, error) {
	return e.employeeRepository.Fetch(adminFkid)
}

func (e *employeeUseCase) FetchSingle(hrmID int, employeeID int) (domain.EmployeeDetail, error) {
	employeeDetail, err := e.employeeRepository.FetchSingle(hrmID, employeeID)
	if err != nil {
		return domain.EmployeeDetail{}, err
	}
	employeeDetail.Outlets, err = e.employeeRepository.FetchEmployeeOutlet(employeeDetail.HrmEmployeeID)
	if err != nil {
		return domain.EmployeeDetail{}, err
	}
	return employeeDetail, nil
}

func (e *employeeUseCase) FetchAddInfo(hrmID int) ([]domain.HrmAddInfo, error) {
	return e.employeeRepository.FetchAddInfo(hrmID)
}

// ImportFromExcel imports employee data from Excel file (DEPRECATED - use ImportFromExcelV2)
func (e *employeeUseCase) ImportFromExcel(filePath string, adminID int) (*domain.ExcelImportResponse, error) {
	log.Info("Starting Excel import for admin ID: %d", adminID)

	// Parse Excel file
	employees, parseErrors, err := e.parseExcelFile(filePath)
	if err != nil {
		return &domain.ExcelImportResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to parse Excel file: %v", err),
			Errors:  parseErrors,
		}, err
	}

	// Initialize response
	response := &domain.ExcelImportResponse{
		Success:       true,
		ProcessedRows: len(employees),
		Errors:        parseErrors,
		ImportSummary: domain.ExcelImportSummary{},
	}

	// If there are parse errors, return early
	if len(parseErrors) > 0 {
		response.Success = false
		response.ErrorCount = len(parseErrors)
		response.Message = fmt.Sprintf("Found %d validation errors in Excel file", len(parseErrors))
		return response, nil
	}

	// Process each employee
	var processErrors []domain.ExcelImportError
	successCount := 0

	for _, employee := range employees {
		processed, err := e.processEmployeeData(employee, adminID)
		if err != nil {
			processErrors = append(processErrors, domain.ExcelImportError{
				Row:     employee.Row,
				Message: err.Error(),
				Value:   employee.NIK,
			})
			continue
		}

		// Upsert employee
		_, err = e.upsertEmployeeFromExcel(processed, adminID)
		if err != nil {
			processErrors = append(processErrors, domain.ExcelImportError{
				Row:     employee.Row,
				Message: fmt.Sprintf("Failed to save employee: %v", err),
				Value:   employee.NIK,
			})
			continue
		}

		successCount++

		// Update summary
		if processed.IsNewEmployee {
			response.ImportSummary.NewEmployees++
		} else {
			response.ImportSummary.UpdatedEmployees++
		}

		if processed.IsNewAccount {
			response.ImportSummary.NewAccounts++
		} else {
			response.ImportSummary.ExistingAccounts++
		}
	}

	// Update response
	response.SuccessCount = successCount
	response.ErrorCount = len(processErrors)
	response.Errors = append(response.Errors, processErrors...)

	if len(processErrors) > 0 {
		response.Success = false
		response.Message = fmt.Sprintf("Processed %d rows: %d successful, %d failed",
			len(employees), successCount, len(processErrors))
	} else {
		response.Message = fmt.Sprintf("Successfully imported %d employees", successCount)
	}

	log.Info("Excel import completed: %d successful, %d failed", successCount, len(processErrors))
	return response, nil
}

// ImportFromExcelV2 imports employee data from Excel file with batch processing and transaction support
func (e *employeeUseCase) ImportFromExcelV2(filePath string, adminID int) (*domain.ExcelImportResponse, error) {
	log.Info("Starting batch Excel import for admin ID: %d", adminID)

	// Parse Excel file
	employees, parseErrors, err := e.parseExcelFile(filePath)
	if err != nil {
		return &domain.ExcelImportResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to parse Excel file: %v", err),
			Errors:  parseErrors,
		}, err
	}

	// Initialize response
	response := &domain.ExcelImportResponse{
		Success:       true,
		ProcessedRows: len(employees),
		Errors:        parseErrors,
		ImportSummary: domain.ExcelImportSummary{},
	}

	// If there are parse errors, return early
	if len(parseErrors) > 0 {
		response.Success = false
		response.ErrorCount = len(parseErrors)
		response.Message = fmt.Sprintf("Found %d validation errors in Excel file", len(parseErrors))
		return response, nil
	}

	// Batch process and validate all employees
	processedEmployees, validationErrors, err := e.batchProcessEmployeeData(employees, adminID)
	if err != nil {
		return &domain.ExcelImportResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to process employee data: %v", err),
			Errors:  validationErrors,
		}, err
	}

	// If there are validation errors, return early (fail fast)
	if len(validationErrors) > 0 {
		response.Success = false
		response.ErrorCount = len(validationErrors)
		response.Errors = append(response.Errors, validationErrors...)
		response.Message = fmt.Sprintf("Found %d validation errors. Import cancelled.", len(validationErrors))
		return response, nil
	}

	// All data is valid, proceed with batch insert/update in transaction
	err = e.batchUpsertEmployees(processedEmployees, adminID, response)
	if err != nil {
		return &domain.ExcelImportResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to save employees: %v", err),
			Errors:  []domain.ExcelImportError{{Message: err.Error()}},
		}, err
	}

	response.SuccessCount = len(processedEmployees)
	response.Message = fmt.Sprintf("Successfully imported %d employees", len(processedEmployees))

	log.Info("Batch Excel import completed: %d successful", len(processedEmployees))
	return response, nil
}

func (e *employeeUseCase) AddEmployeeV1(employee domain.EmployeeData) (int64, error) {
	log.Info("add employee: %v", cast.ToString(employee))
	data, err := validate.FromStruct(employee)
	if err != nil {
		fmt.Printf("error validate form: %v", err)
		return 0, err
	}
	v := data.Create()
	if !v.Validate() {
		fmt.Printf("validate form error: %v", v.Errors)
		return 0, &domain.ValidationException{Message: v.Errors.Error(), ValidatinFieldsErr: v.Errors.All()}
	}

	//handle if client sendin date instead timemillis
	if strings.Contains(employee.JoinDate, "-") {
		//format date 2025-01-02 to timemillis
		// Option A: parse as UTC (result = 2025-01-02T00:00:00Z)
		tUTC, err := time.Parse("2006-01-02", employee.JoinDate)
		if err != nil {
			fmt.Printf("error parsing join date: %v", err)
			return 0, err
		}
		employee.JoinDate = strconv.FormatInt(tUTC.UnixMilli(), 10)
	}

	id, err := e.employeeRepository.AddEmployee(employee)
	if log.IfError(err) {
		return 0, err
	}

	//upload attachment to bucket
	for i, attachmentMap := range employee.UpdateInfoDataWithAttc {
		url, err := e.uploadAttachment(cast.ToString(attachmentMap["attachment"]), employee.EmployeeFkid)
		if log.IfError(err) {
			return 0, err
		}
		employee.UpdateInfoDataWithAttc[i]["attachment"] = url
	}

	for i, attachmentMap := range employee.AddInfoData {
		if attachmentMap["attachment"] == "false" {
			continue
		}
		url, err := e.uploadAttachment(cast.ToString(attachmentMap["attachment"]), employee.EmployeeFkid)
		if log.IfError(err) {
			return 0, err
		}
		employee.AddInfoData[i]["attachment"] = url
	}

	//handle if additional data given
	err = e.employeeRepository.UpdateInfo(employee.UpdateInfoData)
	if err != nil {
		return 0, err
	}
	err = e.employeeRepository.UpdateInfo(employee.UpdateInfoDataWithAttc)
	if err != nil {
		return 0, err
	}

	err = e.employeeRepository.AddInfo(employee.AddInfoData)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (e *employeeUseCase) AddEmployee(employee domain.EmployeeData) (int64, error) {
	return e.employeeRepository.AddEmployee(employee)
}

func (e *employeeUseCase) GetAddInfo(empID int) ([]domain.HrmAddInfo, error) {
	return e.employeeRepository.GetAddInfo(empID)
}

func (e *employeeUseCase) AddInfo(addInfo []map[string]any) error {
	return e.employeeRepository.AddInfo(addInfo)
}

func (e *employeeUseCase) UpdateInfo(updateInfo []map[string]any) error {
	if len(updateInfo) == 0 {
		log.Info("no data to update")
		return nil
	}
	return e.employeeRepository.UpdateInfo(updateInfo)
}

func (e *employeeUseCase) DeleteAttach(where map[string]any) error {
	res, err := e.FetchEmpAttach(cast.ToInt(where["add_id"]))
	if err != nil {
		fmt.Printf("fetching employee attachment error: %v", err)
		return err
	}
	log.Info("attachment: %v", res)
	if len(res) == 0 {
		return nil
	}
	if res[0].Attachment != "false" && res[0].Attachment != "" {
		pathImg := res[0].Attachment
		log.Info("path img: %v", pathImg)
		pathImg = strings.TrimPrefix(pathImg, "https://storage.googleapis.com/uniq-187911.appspot.com/")
		err = bucket.DeleteBucket("uniq-187911.appspot.com", pathImg, true)
		log.IfError(err)
	}

	return e.employeeRepository.DeleteAttach(where)
}

func (e *employeeUseCase) FetchEmpAttach(addID int) ([]domain.EmployeeAttach, error) {
	return e.employeeRepository.FetchEmpAttach(addID)
}

func (e *employeeUseCase) FetchVectorImg(empID int) ([]domain.VectorImg, error) {
	return e.employeeRepository.FetchVectorImg(empID)
}

func (e *employeeUseCase) UpdatePofilePhoto(empID int, img string, vector any) error {
	return e.employeeRepository.UpdatePofilePhoto(empID, img, vector)
}

func (e *employeeUseCase) FetchEmpImg(empID int) (domain.ProfileImage, error) {
	return e.employeeRepository.FetchEmpImg(empID)
}

func (e *employeeUseCase) GetImageVector(file1 multipart.File, file2 multipart.File, fileName string, empID string) error {
	//panggil api ai
	if _, err := os.Stat("temp-images"); os.IsNotExist(err) {
		err := os.Mkdir("temp-images", os.ModePerm)
		if err != nil {
			fmt.Printf("create temp-images folder error: %v", err)
		}
	}
	tempFile, err := ioutil.TempFile("temp-images", "upload-*.jpg")
	if err != nil {
		log.IfError(err)
		fmt.Printf("create temporary folder error: %v", err)
		return err
	}
	defer tempFile.Close()

	fileBytes, err := ioutil.ReadAll(file1)
	if err != nil {
		log.IfError(err)
		return err
	}
	tempFile.Write(fileBytes)
	fileDir, err := os.Getwd()
	if err != nil {
		log.IfError(err)
		return err
	}
	filePath := path.Join(fileDir, tempFile.Name())
	file, err := os.Open(filePath)
	if err != nil {
		log.IfError(err)
		return err
	}

	defer file.Close()
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	writer.WriteField("emp_id", empID)
	part, _ := writer.CreateFormFile("photo", filePath)

	io.Copy(part, file)
	writer.Close()

	resp, _ := http.NewRequest("POST", os.Getenv("API_URL")+"/update_image", body)
	resp.Header.Add("Content-Type", writer.FormDataContentType())
	resp.Body.Close()
	client := &http.Client{}
	response, err := client.Do(resp)
	if err != nil {
		return err
	}
	var res map[string]any
	json.NewDecoder(response.Body).Decode(&res)
	response.Body.Close()

	//simpan vector nya ke db
	id, _ := strconv.Atoi(empID)

	img, err := e.employeeRepository.FetchEmpImg(id)
	if err != nil {
		fmt.Printf("error: %v", err)
		return err
	}

	if img.ProfileImg != "" {
		path_img := img.ProfileImg[55:]
		bucket.DeleteBucket("uniq-187911.appspot.com", path_img, true)
	}

	imgUrl, err := bucket.UploadBucket(file2, fileName, empID, true)
	if err != nil {
		log.IfError(err)
		return err
	}

	err = e.employeeRepository.UpdatePofilePhoto(id, imgUrl, res["img_vector"])
	if err != nil {
		fmt.Printf("error update image vector: %v", err)
		return err
	}

	file1.Close()
	tempFile.Close()
	file.Close()
	err = os.Remove(filePath)
	if err != nil {
		fmt.Printf("removing file error: %v", err)
		return err
	}
	return nil
}

func (e *employeeUseCase) ChangePassword(email string, oldPass string, newPass string) error {
	return e.employeeRepository.ChangePassword(email, oldPass, newPass)
}

func (e *employeeUseCase) FetchEmailNPassword(email string) (domain.EmailNPassword, error) {
	return e.employeeRepository.FetchEmailNPassword(email)
}

func (e *employeeUseCase) InsertUserKey(email string, BcryptStr string) error {
	return e.employeeRepository.InsertUserKey(email, BcryptStr)
}

// GetEmployeeV2 gets detailed employee data with flattened additional info
func (e *employeeUseCase) GetEmployeeV2(businessID int, filter ...domain.EmployeeFilter) ([]map[string]any, error) {
	var employees []domain.Employee
	var err error

	employees, err = e.employeeRepository.FetchWithFilter(businessID, filter[0])
	if err != nil {
		return nil, err
	}

	//if no employee, return empty array
	if len(employees) == 0 {
		return []map[string]any{}, nil
	}

	// Get all employee IDs
	var employeeIDs []int
	for _, emp := range employees {
		employeeIDs = append(employeeIDs, emp.HrmEmployeeID)
	}

	// Get additional info for all employees
	additionalInfo, err := e.employeeRepository.FetchAddInfo(employeeIDs...)
	if err != nil {
		return nil, err
	}

	// Create a map of employee ID to their additional info
	empAdditionalInfo := make(map[int][]domain.HrmAddInfo)
	for _, info := range additionalInfo {
		empAdditionalInfo[info.HrmEmployeeFkid] = append(empAdditionalInfo[info.HrmEmployeeFkid], info)
	}

	// Get all unique titles from additional info
	uniqueTitles := make(map[string]bool)
	for _, info := range additionalInfo {
		uniqueTitles[info.Title] = true
	}

	defaultInfo := map[string]any{}
	for title := range uniqueTitles {
		defaultInfo[title] = ""
	}
	log.Info("unique titles: %v", uniqueTitles)

	// Convert employees to map format and add additional info
	var result []map[string]any
	for _, emp := range employees {
		// Calculate tenure from date_join
		empMap := map[string]any{
			"employee_id":     emp.EmployeeID,
			"hrm_employee_id": emp.HrmEmployeeID,
			"nik":             emp.Nik,
			"name":            emp.Name,
			"employee_salary": emp.EmployeeSallary,
			"type_name":       emp.TypeName,
			"max_leave":       emp.MaxLeave,
			"date_join":       emp.DateJoin,
			"jabatan_fkid":    emp.JabatanFkid,
			"position":        emp.JabatanName,
			"tenure":          FormatTenure(emp.DateJoin),
		}

		// Add empty values for all possible additional info fields
		// for title := range uniqueTitles {
		// 	empMap[title] = ""
		// }

		//set default value for additional info
		empInfo := maputil.Copy(defaultInfo)

		// Fill in the actual values for this employee's additional info
		if info, exists := empAdditionalInfo[emp.HrmEmployeeID]; exists {
			for _, i := range info {
				// empMap[i.Title] = i.Info
				empInfo[i.Title] = i.Info
			}
		}
		empMap["additional_info"] = empInfo
		result = append(result, empMap)
	}

	return result, nil
}
