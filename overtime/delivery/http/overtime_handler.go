package http

import (
	"github.com/gofiber/fiber/v2"
	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

// OvertimeHandler struct
type OvertimeHandler struct {
	OvertimeUseCase domain.OvertimeUseCase
}

// NewOvertimeHandler creates a new overtime handler and sets up routes
func NewOvertimeHandler(app *fiber.App, uc domain.OvertimeUseCase) {
	handler := &OvertimeHandler{OvertimeUseCase: uc}

	// API routes
	v1 := app.Group("/v1")
	v1.Post("/overtime-policies", handler.AddOvertimePolicy)
	v1.Get("/overtime-policies", handler.FetchOvertimePolicies)
}

// AddOvertimePolicy creates a new overtime policy
// @Summary Create a new overtime policy
// @Description Create a new overtime policy with support for three types: none (exact duration), simple (consistent rounding), and tiered (custom rounding rules). For simple rounding, rounding_minutes and rounding_method are required. For tiered rounding, rounding_tiers array is required.
// @Tags overtime
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param policy body domain.OvertimePolicyRequest true "Overtime policy information"
// @Success 201 {object} fiber.Map "Successfully created overtime policy with policy ID"
// @Failure 400 {object} fiber.Map "Bad request - validation error"
// @Failure 401 {object} fiber.Map "Unauthorized - invalid session"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/overtime-policies [post]
func (o *OvertimeHandler) AddOvertimePolicy(c *fiber.Ctx) error {
	// Get user session to extract admin_fkid
	user := domain.GetUserSessionFiber(c)
	if user.BusinessId == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(&fiber.Map{
			"message": "Unauthorized - invalid session",
			"status":  0,
		})
	}

	// Parse request body
	var policyRequest domain.OvertimePolicyRequest
	if err := c.BodyParser(&policyRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Invalid request body",
			"status":  0,
			"error":   err.Error(),
		})
	}

	// Perform basic validation using gookit/validate
	data, err := validate.FromStruct(policyRequest)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error validating request",
			"status":  0,
			"error":   err.Error(),
		})
	}

	v := data.Create()
	if !v.Validate() {
		// Return validation errors in a user-friendly format
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Validation failed",
			"status":  0,
			"errors":  v.Errors.All(),
		})
	}

	// Create the overtime policy
	policyID, err := o.OvertimeUseCase.AddOvertimePolicy(policyRequest, user.BusinessId)
	if err != nil {
		if validationErr, ok := err.(domain.ValidationException); ok {
			return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
				"message": validationErr.Message,
				"status":  0,
				"errors":  validationErr.ValidatinFieldsErr,
			})
		}
		log.IfError(err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Failed to create overtime policy",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(&fiber.Map{
		"message":   "Overtime policy created successfully",
		"status":    1,
		"policy_id": policyID,
	})
}

// FetchOvertimePolicies retrieves all overtime policies for the authenticated user
// @Summary Get all overtime policies
// @Description Get all overtime policies filtered by the authenticated user's business ID (admin_fkid). Optionally filter by rounding_type or policy_name using query parameters.
// @Tags overtime
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param rounding_type query string false "Filter by rounding type (none, simple, tiered)"
// @Param policy_name query string false "Filter by policy name (partial match)"
// @Success 200 {array} domain.OvertimePolicyDetail "List of overtime policies with rounding tiers"
// @Failure 401 {object} fiber.Map "Unauthorized - invalid session"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/overtime-policies [get]
func (o *OvertimeHandler) FetchOvertimePolicies(c *fiber.Ctx) error {
	// Get user session to extract admin_fkid
	user := domain.GetUserSessionFiber(c)
	if user.BusinessId == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(&fiber.Map{
			"message": "Unauthorized - invalid session",
			"status":  0,
		})
	}

	// Parse optional filter parameters
	var filter domain.OvertimePolicyFilter

	// Parse rounding_type query parameter if provided
	if roundingType := c.Query("rounding_type"); roundingType != "" {
		// Validate rounding type
		validTypes := []string{"none", "simple", "tiered"}
		for _, validType := range validTypes {
			if roundingType == validType {
				filter.RoundingType = roundingType
				break
			}
		}
	}

	// Parse policy_name query parameter if provided
	if policyName := c.Query("policy_name"); policyName != "" {
		filter.PolicyName = policyName
	}

	// Fetch overtime policies filtered by admin_fkid (user.BusinessId) and optional filters
	policies, err := o.OvertimeUseCase.FetchOvertimePolicies(user.BusinessId, filter)
	if err != nil {
		log.IfError(err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "error fetching overtime policies",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.JSON(policies)
}
