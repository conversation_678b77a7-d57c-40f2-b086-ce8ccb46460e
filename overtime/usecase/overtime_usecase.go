package usecase

import (
	"fmt"
	"sort"

	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

type overtimeUseCase struct {
	overtimeRepository domain.OvertimeRepository
}

// NewOvertimeUseCase creates a new overtime use case
func NewOvertimeUseCase(or domain.OvertimeRepository) domain.OvertimeUseCase {
	// Register custom validators
	validate.AddValidator("overtime_policy_consistency", validateOvertimePolicyConsistency)
	validate.AddValidator("tiered_rounding_rules", validateTieredRoundingRules)
	return &overtimeUseCase{overtimeRepository: or}
}

// validateOvertimePolicyConsistency validates that the policy fields are consistent with the rounding type
func validateOvertimePolicyConsistency(val interface{}) bool {
	policy, ok := val.(domain.OvertimePolicyRequest)
	if !ok {
		return false
	}

	switch policy.RoundingType {
	case "none":
		// For 'none', rounding_minutes and rounding_method should be nil
		return policy.RoundingMinutes == nil && policy.RoundingMethod == nil && len(policy.RoundingTiers) == 0
	case "simple":
		// For 'simple', rounding_minutes and rounding_method should be provided
		return policy.RoundingMinutes != nil && policy.RoundingMethod != nil && len(policy.RoundingTiers) == 0
	case "tiered":
		// For 'tiered', rounding_tiers should be provided
		return policy.RoundingMinutes == nil && policy.RoundingMethod == nil && len(policy.RoundingTiers) > 0
	default:
		return false
	}
}

// validateTieredRoundingRules validates that tiered rounding rules are logical
func validateTieredRoundingRules(val interface{}) bool {
	tiers, ok := val.([]domain.OvertimeRoundingTierRequest)
	if !ok {
		return false
	}

	if len(tiers) == 0 {
		return true // Empty tiers are valid (will be checked by policy consistency)
	}

	// Sort tiers by up_to_minutes for validation
	sortedTiers := make([]domain.OvertimeRoundingTierRequest, len(tiers))
	copy(sortedTiers, tiers)
	sort.Slice(sortedTiers, func(i, j int) bool {
		return sortedTiers[i].UpToMinutes < sortedTiers[j].UpToMinutes
	})

	// Check for duplicates and logical progression
	for i := 0; i < len(sortedTiers); i++ {
		tier := sortedTiers[i]

		// Check that rounded_value_minutes is not greater than up_to_minutes
		if tier.RoundedValueMinutes > tier.UpToMinutes {
			return false
		}

		// Check for duplicates
		if i > 0 && sortedTiers[i-1].UpToMinutes == tier.UpToMinutes {
			return false
		}
	}

	return true
}

// AddOvertimePolicy creates a new overtime policy with business logic validation
func (o *overtimeUseCase) AddOvertimePolicy(policy domain.OvertimePolicyRequest, adminFkid int) (int64, error) {
	// Use gookit/validate for struct validation
	data, err := validate.FromStruct(policy)
	if err != nil {
		return 0, fmt.Errorf("error creating validator: %v", err)
	}

	v := data.Create()
	if !v.Validate() {
		return 0, domain.ValidationException{
			Message:            v.Errors.Error(),
			ValidatinFieldsErr: v.Errors.All(),
		}
	}

	// Additional business logic validation
	if err := o.validateBusinessRules(policy); err != nil {
		return 0, domain.ValidationException{
			Message:            err.Error(),
			ValidatinFieldsErr: map[string]map[string]string{"business_rules": {"error": err.Error()}},
		}
	}

	// Call repository to create the overtime policy
	policyID, err := o.overtimeRepository.AddOvertimePolicy(policy, adminFkid)
	if err != nil {
		log.IfError(err)
		return 0, err
	}

	log.Info("Created overtime policy with ID: %d for admin: %d", policyID, adminFkid)
	return policyID, nil
}

// validateBusinessRules performs additional business logic validation
func (o *overtimeUseCase) validateBusinessRules(policy domain.OvertimePolicyRequest) error {
	switch policy.RoundingType {
	case "simple":
		// Validate simple rounding rules
		if policy.RoundingMinutes != nil && *policy.RoundingMinutes <= 0 {
			return fmt.Errorf("rounding_minutes must be greater than 0 for simple rounding")
		}
		if policy.RoundingMethod != nil {
			validMethods := []string{"round_down", "round_nearest", "round_up"}
			isValid := false
			for _, method := range validMethods {
				if *policy.RoundingMethod == method {
					isValid = true
					break
				}
			}
			if !isValid {
				return fmt.Errorf("invalid rounding_method: %s", *policy.RoundingMethod)
			}
		}

	case "tiered":
		// Validate tiered rounding rules
		if len(policy.RoundingTiers) == 0 {
			return fmt.Errorf("at least one rounding tier is required for tiered rounding")
		}

		// Check that tiers cover a reasonable range
		maxUpTo := 0
		for _, tier := range policy.RoundingTiers {
			if tier.UpToMinutes > maxUpTo {
				maxUpTo = tier.UpToMinutes
			}
		}

		// Ensure the highest tier covers at least 60 minutes (1 hour)
		if maxUpTo < 60 {
			return fmt.Errorf("tiered rounding should cover at least 60 minutes")
		}
	}

	return nil
}

// FetchOvertimePolicies retrieves overtime policies with optional filters
func (o *overtimeUseCase) FetchOvertimePolicies(adminFkid int, filter domain.OvertimePolicyFilter) ([]domain.OvertimePolicyDetail, error) {
	policies, err := o.overtimeRepository.FetchOvertimePolicies(adminFkid, filter)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	log.Info("Fetched %d overtime policies for admin: %d", len(policies), adminFkid)
	return policies, nil
}
