package mysql

import (
	"database/sql"
	"fmt"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLOvertimeRepository struct {
	mysql.Repository
}

// NewMySQLOvertimeRepository creates a new MySQL overtime repository
func NewMySQLOvertimeRepository(conn *sql.DB) domain.OvertimeRepository {
	return &mySQLOvertimeRepository{mysql.Repository{Conn: conn}}
}

// AddOvertimePolicy creates a new overtime policy (simple implementation)
func (m *mySQLOvertimeRepository) AddOvertimePolicy(policy domain.OvertimePolicyRequest, adminFkid int) (int64, error) {
	return m.AddOvertimePolicyWithTiers(policy, adminFkid)
}

// AddOvertimePolicyWithTiers creates a new overtime policy with optional tiers using transaction
func (m *mySQLOvertimeRepository) AddOvertimePolicyWithTiers(policy domain.OvertimePolicyRequest, adminFkid int) (int64, error) {
	var policyID int64

	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Prepare overtime policy data
		now := time.Now().UnixMilli()
		policyData := map[string]interface{}{
			"policy_name":   policy.PolicyName,
			"description":   policy.Description,
			"rounding_type": policy.RoundingType,
			"admin_fkid":    adminFkid,
			"created_at":    now,
			"updated_at":    now,
		}

		// Handle rounding fields based on type
		switch policy.RoundingType {
		case "none":
			policyData["rounding_minutes"] = nil
			policyData["rounding_method"] = nil
		case "simple":
			if policy.RoundingMinutes == nil || policy.RoundingMethod == nil {
				return fmt.Errorf("rounding_minutes and rounding_method are required for simple rounding type")
			}
			policyData["rounding_minutes"] = *policy.RoundingMinutes
			policyData["rounding_method"] = *policy.RoundingMethod
		case "tiered":
			policyData["rounding_minutes"] = nil
			policyData["rounding_method"] = nil
			if len(policy.RoundingTiers) == 0 {
				return fmt.Errorf("rounding_tiers are required for tiered rounding type")
			}
		}

		// Insert overtime policy record
		result := tx.Insert("hrm_overtime_policies", policyData)
		id, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("failed to get last insert ID: %v", err)
		}

		policyID = id

		// Insert rounding tiers if this is a tiered policy
		if policy.RoundingType == "tiered" && len(policy.RoundingTiers) > 0 {
			tierDataSlice := make([]map[string]interface{}, len(policy.RoundingTiers))
			for i, tier := range policy.RoundingTiers {
				tierDataSlice[i] = map[string]interface{}{
					"overtime_policy_id":    policyID,
					"up_to_minutes":         tier.UpToMinutes,
					"rounded_value_minutes": tier.RoundedValueMinutes,
				}
			}

			// Bulk insert tiers
			tx.BulkInsert("hrm_overtime_rounding_tiers", tierDataSlice)
		}

		return nil
	})

	if err != nil {
		log.IfError(err)
		return 0, err
	}

	return policyID, nil
}

// FetchOvertimePolicies retrieves overtime policies with optional filters
func (m *mySQLOvertimeRepository) FetchOvertimePolicies(adminFkid int, filter domain.OvertimePolicyFilter) ([]domain.OvertimePolicyDetail, error) {
	// Build the main query for policies
	query := `SELECT 
		id, policy_name, description, rounding_type, 
		rounding_minutes, rounding_method, admin_fkid, 
		created_at, updated_at
	FROM hrm_overtime_policies 
	WHERE admin_fkid = @adminFkid`

	// Build parameters map
	params := map[string]any{
		"adminFkid": adminFkid,
	}

	// Add optional filters
	if filter.RoundingType != "" {
		query += ` AND rounding_type = @roundingType`
		params["roundingType"] = filter.RoundingType
	}

	if filter.PolicyName != "" {
		query += ` AND policy_name LIKE @policyName`
		params["policyName"] = "%" + filter.PolicyName + "%"
	}

	query += ` ORDER BY created_at DESC`

	// Use MapParam to handle named parameters
	query, paramValues := mysql.MapParam(query, params)

	var policies []domain.OvertimePolicy
	err := m.Query(query, paramValues...).Model(&policies)
	if log.IfError(err) {
		return nil, err
	}

	// Convert to detail format and fetch tiers for tiered policies
	var policyDetails []domain.OvertimePolicyDetail
	for _, policy := range policies {
		detail := domain.OvertimePolicyDetail{
			OvertimePolicy: policy,
		}

		// Fetch tiers if this is a tiered policy
		if policy.RoundingType == "tiered" {
			tiers, err := m.fetchRoundingTiers(policy.ID)
			if err != nil {
				log.IfError(err)
				return nil, err
			}
			detail.RoundingTiers = tiers
		}

		policyDetails = append(policyDetails, detail)
	}

	return policyDetails, nil
}

// fetchRoundingTiers retrieves rounding tiers for a specific policy
func (m *mySQLOvertimeRepository) fetchRoundingTiers(policyID int64) ([]domain.OvertimeRoundingTier, error) {
	query := `SELECT 
		id, overtime_policy_id, up_to_minutes, rounded_value_minutes
	FROM hrm_overtime_rounding_tiers 
	WHERE overtime_policy_id = ? 
	ORDER BY up_to_minutes ASC`

	var tiers []domain.OvertimeRoundingTier
	err := m.Query(query, policyID).Model(&tiers)
	if log.IfError(err) {
		return nil, err
	}

	return tiers, nil
}
